{% extends 'base.html' %}

{% block title %}Dashboard - Valet Parking System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-tachometer-alt"></i> Dashboard</h1>
                <div class="text-muted">
                    <i class="fas fa-clock"></i> Last updated: <span id="lastUpdated">--</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="stats-card text-center">
                <div class="icon text-primary">
                    <i class="fas fa-parking"></i>
                </div>
                <div class="number" id="totalSlots">--</div>
                <div class="label">Total Parking Slots</div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="stats-card text-center">
                <div class="icon text-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="number" id="availableSlots">--</div>
                <div class="label">Available Slots</div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="stats-card text-center">
                <div class="icon text-warning">
                    <i class="fas fa-car"></i>
                </div>
                <div class="number" id="occupiedSlots">--</div>
                <div class="label">Occupied Slots</div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="stats-card text-center">
                <div class="icon text-info">
                    <i class="fas fa-exchange-alt"></i>
                </div>
                <div class="number" id="pendingTransactions">--</div>
                <div class="label">Pending Transactions</div>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="row">
        <!-- Recent Transactions -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list"></i> Recent Transactions</h5>
                </div>
                <div class="card-body">
                    <div id="transactionsLoading" class="loading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover" id="transactionsTable" style="display: none;">
                            <thead>
                                <tr>
                                    <th>Customer</th>
                                    <th>Plate Number</th>
                                    <th>Slot</th>
                                    <th>Status</th>
                                    <th>Time</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="transactionsBody">
                            </tbody>
                        </table>
                    </div>
                    <div id="noTransactions" class="text-center text-muted py-4" style="display: none;">
                        <i class="fas fa-inbox fa-3x mb-3"></i>
                        <p>No recent transactions</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if user.role == 'company_admin' %}
                        <a href="{% url 'frontend:companies' %}" class="btn btn-outline-primary">
                            <i class="fas fa-building"></i> Manage Companies
                        </a>
                        {% endif %}
                        <a href="{% url 'frontend:parking' %}" class="btn btn-outline-success">
                            <i class="fas fa-plus"></i> Add Parking Slot
                        </a>
                        <a href="{% url 'frontend:transactions' %}" class="btn btn-outline-info">
                            <i class="fas fa-eye"></i> View All Transactions
                        </a>
                        <button class="btn btn-outline-warning" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i> Refresh Data
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- System Status -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-heartbeat"></i> System Status</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>API Status</span>
                        <span class="status-badge status-delivered" id="apiStatus">Online</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>WhatsApp Integration</span>
                        <span class="status-badge status-delivered" id="whatsappStatus">Active</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Database</span>
                        <span class="status-badge status-delivered" id="dbStatus">Connected</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Dashboard specific JavaScript
let dashboardData = {};

async function loadDashboardData() {
    try {
        // Load parking slots
        const slots = await apiRequest('/api/parking/slots/');
        const totalSlots = slots.length;
        const occupiedSlots = slots.filter(slot => slot.is_occupied).length;
        const availableSlots = totalSlots - occupiedSlots;
        
        // Update stats
        document.getElementById('totalSlots').textContent = totalSlots;
        document.getElementById('availableSlots').textContent = availableSlots;
        document.getElementById('occupiedSlots').textContent = occupiedSlots;
        
        // Load transactions to get pending count
        const transactions = await apiRequest('/api/parking/transactions/');
        const pendingTransactions = transactions.filter(t =>
            t.status === 'pending_park' || t.status === 'pending_retrieve'
        ).length;
        document.getElementById('pendingTransactions').textContent = pendingTransactions;

        // Update last updated time
        document.getElementById('lastUpdated').textContent = new Date().toLocaleTimeString();

        // Load recent transactions
        loadRecentTransactions(transactions.slice(0, 5)); // Show latest 5
        
    } catch (error) {
        console.error('Failed to load dashboard data:', error);
        showAlert('Failed to load dashboard data', 'danger');
    }
}

function loadRecentTransactions(transactions = []) {
    document.getElementById('transactionsLoading').style.display = 'none';

    if (transactions.length === 0) {
        document.getElementById('noTransactions').style.display = 'block';
        document.getElementById('transactionsTable').style.display = 'none';
        return;
    }

    const tbody = document.getElementById('transactionsBody');
    const transactionsHTML = transactions.map(transaction => `
        <tr>
            <td>${transaction.customer ? transaction.customer.phone_number : 'N/A'}</td>
            <td><strong>${transaction.plate_number || 'N/A'}</strong></td>
            <td>${transaction.slot ? transaction.slot.name : 'N/A'}</td>
            <td>${getStatusBadge(transaction.status)}</td>
            <td>${new Date(transaction.requested_at).toLocaleString()}</td>
            <td>
                <a href="/transactions/" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-eye"></i> View
                </a>
            </td>
        </tr>
    `).join('');

    tbody.innerHTML = transactionsHTML;
    document.getElementById('transactionsTable').style.display = 'table';
    document.getElementById('noTransactions').style.display = 'none';
}

function refreshDashboard() {
    showAlert('Refreshing dashboard...', 'info');
    loadDashboardData();
}

// Load dashboard data when page loads
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
    
    // Auto-refresh every 30 seconds
    setInterval(loadDashboardData, 30000);
});
</script>
{% endblock %}
