{% extends 'base.html' %}

{% block title %}Register Company - Valet Parking System{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center" style="min-height: 80vh; align-items: center;">
        <div class="col-md-8 col-lg-6">
            <div class="form-container">
                <div class="text-center mb-4">
                    <i class="fas fa-building text-primary" style="font-size: 3rem;"></i>
                    <h2 class="mt-3">Register Your Company</h2>
                    <p class="text-muted">Start managing your valet parking operations</p>
                </div>
                
                <div id="alerts"></div>
                
                <form id="registerCompanyForm">
                    <!-- Company Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-building"></i> Company Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="companyName" class="form-label">Company Name *</label>
                                <input type="text" class="form-control" id="companyName" name="company_name" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="companyPhone" class="form-label">Company Phone *</label>
                                <input type="tel" class="form-control" id="companyPhone" name="company_phone" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="companyLocation" class="form-label">Company Address *</label>
                                <textarea class="form-control" id="companyLocation" name="company_location" rows="3" required></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Admin User Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-user-tie"></i> Admin User Details</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="firstName" class="form-label">First Name *</label>
                                        <input type="text" class="form-control" id="firstName" name="first_name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="lastName" class="form-label">Last Name *</label>
                                        <input type="text" class="form-control" id="lastName" name="last_name" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="username" class="form-label">Username *</label>
                                <input type="text" class="form-control" id="username" name="username" required>
                                <div class="form-text">This will be your login username</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Password *</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <div class="form-text">Minimum 8 characters</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">Confirm Password *</label>
                                <input type="password" class="form-control" id="confirmPassword" name="confirm_password" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="agreeTerms" required>
                        <label class="form-check-label" for="agreeTerms">
                            I agree to the <a href="#" class="text-primary">Terms of Service</a> and <a href="#" class="text-primary">Privacy Policy</a>
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100 mb-3">
                        <i class="fas fa-building"></i> Register Company
                    </button>
                </form>
                
                <div class="text-center">
                    <p class="mb-0">Already have an account? 
                        <a href="{% url 'frontend:login' %}" class="text-primary">Sign in here</a>
                    </p>
                    <p class="mb-0">Want to join as an employee? 
                        <a href="{% url 'frontend:register_employee' %}" class="text-primary">Register as Employee</a>
                    </p>
                </div>
                
                <hr class="my-4">
                
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> What happens next?</h6>
                    <ul class="mb-0">
                        <li>Your company will be registered with a unique company code</li>
                        <li>You'll become the company admin with full access</li>
                        <li>You can create parking slots and manage employees</li>
                        <li>Employees can join using your company code</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('registerCompanyForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const data = Object.fromEntries(formData);
    
    // Validate passwords match
    if (data.password !== data.confirm_password) {
        showAlert('Passwords do not match', 'danger');
        return;
    }
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating company...';
    submitBtn.disabled = true;
    
    try {
        const response = await fetch('/api/auth/register-company/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify(data)
        });
        
        if (response.ok) {
            const result = await response.json();
            showAlert(`Company registered successfully! Your company code is: <strong>${result.company_code}</strong>`, 'success');
            setTimeout(() => {
                window.location.href = '/login/';
            }, 3000);
        } else {
            const error = await response.json();
            showAlert(error.message || 'Registration failed. Please try again.', 'danger');
        }
    } catch (error) {
        console.error('Registration error:', error);
        showAlert('Registration failed. Please try again.', 'danger');
    } finally {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
});

// Real-time password validation
document.getElementById('confirmPassword').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (confirmPassword && password !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
    }
});

// Function to get CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
